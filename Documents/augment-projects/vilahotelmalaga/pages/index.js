import { useState } from 'react';
import Head from 'next/head';
import HotelCard from '../components/HotelCard';
import styles from '../styles/Home.module.css';
import hotelData from '../data/hotels.json';

export default function Home() {
  const [hotels, setHotels] = useState(hotelData.hotels);
  const [filters, setFilters] = useState({
    minRating: '',
    maxPrice: '',
    stars: ''
  });

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value
    });
  };

  const applyFilters = () => {
    let filteredHotels = [...hotelData.hotels];
    
    if (filters.minRating) {
      filteredHotels = filteredHotels.filter(h => h.rating >= parseFloat(filters.minRating));
    }
    
    if (filters.maxPrice) {
      filteredHotels = filteredHotels.filter(h => h.price_range.max <= parseFloat(filters.maxPrice));
    }
    
    if (filters.stars) {
      filteredHotels = filteredHotels.filter(h => h.stars === parseInt(filters.stars));
    }
    
    setHotels(filteredHotels);
  };

  return (
    <div className={styles.container}>
      <Head>
        <title>Vila Hotel Málaga - Last Minute Aanbiedingen</title>
        <meta name="description" content="Vind de beste last-minute hotelaanbiedingen in Málaga" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className={styles.main}>
        <h1 className={styles.title}>
          Last Minute Hotels in Málaga
        </h1>

        <div className={styles.filters}>
          <div className={styles.filterItem}>
            <label htmlFor="minRating">Minimale beoordeling:</label>
            <input
              type="number"
              id="minRating"
              name="minRating"
              min="0"
              max="10"
              step="0.1"
              value={filters.minRating}
              onChange={handleFilterChange}
              className={styles.filterInput}
            />
          </div>
          
          <div className={styles.filterItem}>
            <label htmlFor="maxPrice">Maximale prijs:</label>
            <input
              type="number"
              id="maxPrice"
              name="maxPrice"
              min="0"
              value={filters.maxPrice}
              onChange={handleFilterChange}
              className={styles.filterInput}
            />
          </div>
          
          <div className={styles.filterItem}>
            <label htmlFor="stars">Sterren:</label>
            <select
              id="stars"
              name="stars"
              value={filters.stars}
              onChange={handleFilterChange}
              className={styles.filterInput}
            >
              <option value="">Alle sterren</option>
              <option value="2">2 sterren</option>
              <option value="3">3 sterren</option>
              <option value="4">4 sterren</option>
              <option value="5">5 sterren</option>
            </select>
          </div>
          
          <button className={styles.filterButton} onClick={applyFilters}>
            Filters toepassen
          </button>
        </div>

        <div className={styles.hotelsGrid}>
          {hotels.length > 0 ? (
            hotels.map(hotel => (
              <HotelCard key={hotel.id} hotel={hotel} />
            ))
          ) : (
            <p className={styles.noResults}>Geen hotels gevonden die aan de criteria voldoen.</p>
          )}
        </div>
      </main>

      <footer className={styles.footer}>
        <p>© 2023 Vila Hotel Málaga - Alle rechten voorbehouden</p>
      </footer>
    </div>
  );
} 