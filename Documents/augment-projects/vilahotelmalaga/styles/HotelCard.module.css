.card {
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: white;
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-4px);
}

.imageContainer {
  position: relative;
  height: 250px;
  width: 100%;
}

.image {
  object-fit: cover;
}

.stars {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20px;
  padding: 5px 10px;
  color: #FFD700;
}

.star {
  margin-right: 2px;
}

.content {
  padding: 1.5rem;
}

.title {
  font-size: 1.5rem;
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #333;
}

.rating {
  display: inline-block;
  margin-bottom: 0.75rem;
  color: #666;
}

.ratingScore {
  font-weight: bold;
  font-size: 1.2rem;
  color: #2C3E50;
}

.location {
  color: #666;
  margin-bottom: 1rem;
}

.price {
  margin-bottom: 1rem;
}

.priceRange {
  font-weight: bold;
  font-size: 1.3rem;
  color: #e74c3c;
}

.perNight {
  font-size: 0.9rem;
  color: #666;
}

.amenities {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 1rem;
}

.amenity {
  background-color: #f8f9fa;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 0.85rem;
  color: #666;
}

.info {
  margin-bottom: 1rem;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  padding: 1rem 0;
}

.infoItem {
  margin-bottom: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.label {
  font-weight: bold;
  color: #333;
}

.policy {
  font-size: 0.9rem;
  color: #27ae60;
  margin-bottom: 1.5rem;
}

.button {
  display: block;
  width: 100%;
  padding: 0.75rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.button:hover {
  background-color: #2980b9;
}

@media (min-width: 768px) {
  .card {
    flex-direction: row;
    height: 250px;
  }
  
  .imageContainer {
    width: 40%;
  }
  
  .content {
    width: 60%;
    overflow-y: auto;
  }
} 