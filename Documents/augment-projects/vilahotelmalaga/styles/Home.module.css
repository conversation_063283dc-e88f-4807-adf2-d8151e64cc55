.container {
  min-height: 100vh;
  padding: 0 1rem;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.main {
  flex: 1;
  padding: 2rem 0;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.title {
  margin: 0;
  line-height: 1.15;
  font-size: 3rem;
  text-align: center;
  color: #2c3e50;
  margin-bottom: 2rem;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filterItem {
  display: flex;
  flex-direction: column;
  min-width: 200px;
  flex: 1;
}

.filterItem label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.filterInput {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.filterButton {
  align-self: flex-end;
  padding: 0.6rem 1.5rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 1.5rem;
}

.filterButton:hover {
  background-color: #2980b9;
}

.hotelsGrid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.noResults {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-size: 1.2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.footer {
  width: 100%;
  padding: 1.5rem 0;
  border-top: 1px solid #eaeaea;
  text-align: center;
  background-color: white;
}

.footer p {
  color: #666;
}

@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .filterItem {
    min-width: 100%;
  }
} 