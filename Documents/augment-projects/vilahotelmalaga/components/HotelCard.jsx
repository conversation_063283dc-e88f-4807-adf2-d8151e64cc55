import React from 'react';
import Image from 'next/image';
import styles from '../styles/HotelCard.module.css';

const HotelCard = ({ hotel }) => {
  return (
    <div className={styles.card}>
      <div className={styles.imageContainer}>
        <Image 
          src={hotel.image} 
          alt={hotel.name}
          className={styles.image}
          width={400}
          height={250}
          objectFit="cover"
        />
        <div className={styles.stars}>
          {Array.from({ length: hotel.stars }).map((_, i) => (
            <span key={i} className={styles.star}>★</span>
          ))}
        </div>
      </div>
      
      <div className={styles.content}>
        <h3 className={styles.title}>{hotel.name}</h3>
        <div className={styles.rating}>
          <span className={styles.ratingScore}>{hotel.rating}</span> / 10
        </div>
        <div className={styles.location}>{hotel.location.description}</div>
        <div className={styles.price}>
          <span className={styles.priceRange}>
            €{hotel.price_range.min} - €{hotel.price_range.max}
          </span>
          <span className={styles.perNight}> / nacht</span>
        </div>
        
        <div className={styles.amenities}>
          {hotel.amenities.map((amenity, index) => (
            <span key={index} className={styles.amenity}>{amenity}</span>
          ))}
        </div>
        
        <div className={styles.info}>
          <div className={styles.infoItem}>
            <span className={styles.label}>Check-in:</span> {hotel.check_in}
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Check-out:</span> {hotel.check_out}
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Parkeren:</span> €{hotel.parking_fee}/dag
          </div>
        </div>
        
        <div className={styles.policy}>{hotel.cancellation_policy}</div>
        
        <button className={styles.button}>Bekijk beschikbaarheid</button>
      </div>
    </div>
  );
};

export default HotelCard; 