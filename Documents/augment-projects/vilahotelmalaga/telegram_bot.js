require('dotenv').config();
const { Telegraf, <PERSON><PERSON> } = require('telegraf');
const fs = require('fs');

// Telegram bot token uit .env bestand
const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);

// Data inladen
const hotelsData = JSON.parse(fs.readFileSync('./data/hotels.json', 'utf8'));

// Start commando
bot.start((ctx) => {
  ctx.reply(
    `👋 Welkom bij de Vila Hotel Málaga bot!\n\nIk kan je helpen met het vinden van de beste hotels in Málaga. Gebruik /hotels om alle hotels te bekijken of /filter om te filteren op prijs en beoordeling.`,
    Markup.keyboard([
      ['🏨 Alle hotels', '🔍 Hotels filteren'],
      ['⭐ Beste beoordelingen', '💰 Goedkoopste hotels']
    ]).resize()
  );
});

// Help commando
bot.help((ctx) => {
  ctx.reply(`
Beschikbare commando's:
/start - Start de bot
/hotels - Toon alle hotels
/filter - Filter hotels op criteria
/hotel [id] - Toon details van een specifiek hotel
/beste - Toon hotels met de beste beoordelingen
/goedkoop - Toon de goedkoopste hotels
  `);
});

// Toon alle hotels
const showAllHotels = (ctx) => {
  let message = '🏨 *Hotels in Málaga:*\n\n';
  
  hotelsData.hotels.forEach(hotel => {
    message += `*${hotel.id}. ${hotel.name}* (${hotel.stars}⭐)\n`;
    message += `Beoordeling: ${hotel.rating}/10\n`;
    message += `Prijs: €${hotel.price_range.min} - €${hotel.price_range.max}\n`;
    message += `Voor meer info: /hotel_${hotel.id}\n\n`;
  });
  
  ctx.replyWithMarkdown(message);
};

bot.command('hotels', showAllHotels);
bot.hears('🏨 Alle hotels', showAllHotels);

// Toon specifiek hotel
bot.hears(/\/hotel_(\d+)/, (ctx) => {
  const hotelId = parseInt(ctx.match[1]);
  const hotel = hotelsData.hotels.find(h => h.id === hotelId);
  
  if (!hotel) {
    return ctx.reply('Hotel niet gevonden.');
  }
  
  let message = `🏨 *${hotel.name}* (${hotel.stars}⭐)\n\n`;
  message += `📊 *Beoordeling:* ${hotel.rating}/10\n`;
  message += `💰 *Prijs:* €${hotel.price_range.min} - €${hotel.price_range.max} per nacht\n`;
  message += `📍 *Locatie:* ${hotel.location.description}\n`;
  message += `🕒 *Check-in:* ${hotel.check_in}, *Check-out:* ${hotel.check_out}\n`;
  message += `🚗 *Parkeren:* €${hotel.parking_fee}/dag\n\n`;
  
  message += `✨ *Voorzieningen:*\n`;
  hotel.amenities.forEach(amenity => {
    message += `- ${amenity}\n`;
  });
  
  message += `\n📝 *${hotel.cancellation_policy}*`;
  
  ctx.replyWithMarkdown(message, Markup.inlineKeyboard([
    Markup.button.url('Boek dit hotel', `https://vila-hotel-malaga.example/booking/${hotel.id}`)
  ]));
});

// Filter functionaliteit
bot.command('filter', (ctx) => {
  ctx.reply('Wat is je maximale prijs per nacht?');
  ctx.session = { filterState: 'awaiting_max_price' };
});

bot.hears('🔍 Hotels filteren', (ctx) => {
  ctx.reply('Wat is je maximale prijs per nacht?');
  ctx.session = { filterState: 'awaiting_max_price' };
});

// Filter op maximale prijs
bot.on('text', (ctx) => {
  if (!ctx.session || !ctx.session.filterState) return;
  
  if (ctx.session.filterState === 'awaiting_max_price') {
    const maxPrice = parseInt(ctx.message.text);
    
    if (isNaN(maxPrice)) {
      return ctx.reply('Voer een geldig bedrag in (alleen getallen).');
    }
    
    ctx.session.maxPrice = maxPrice;
    ctx.session.filterState = 'awaiting_min_rating';
    ctx.reply('Wat is je minimale beoordeling? (0-10)');
    
  } else if (ctx.session.filterState === 'awaiting_min_rating') {
    const minRating = parseFloat(ctx.message.text);
    
    if (isNaN(minRating) || minRating < 0 || minRating > 10) {
      return ctx.reply('Voer een geldige beoordeling in (0-10).');
    }
    
    // Filter de hotels op basis van de criteria
    const filteredHotels = hotelsData.hotels.filter(hotel => 
      hotel.price_range.max <= ctx.session.maxPrice && 
      hotel.rating >= minRating
    );
    
    if (filteredHotels.length === 0) {
      ctx.reply('Geen hotels gevonden die aan je criteria voldoen.');
    } else {
      let message = `🏨 *${filteredHotels.length} hotels gevonden:*\n\n`;
      
      filteredHotels.forEach(hotel => {
        message += `*${hotel.id}. ${hotel.name}* (${hotel.stars}⭐)\n`;
        message += `Beoordeling: ${hotel.rating}/10\n`;
        message += `Prijs: €${hotel.price_range.min} - €${hotel.price_range.max}\n`;
        message += `Voor meer info: /hotel_${hotel.id}\n\n`;
      });
      
      ctx.replyWithMarkdown(message);
    }
    
    // Reset de session
    delete ctx.session.filterState;
    delete ctx.session.maxPrice;
  }
});

// Beste beoordelingen
const showBestRated = (ctx) => {
  const sortedHotels = [...hotelsData.hotels].sort((a, b) => b.rating - a.rating);
  const bestHotels = sortedHotels.slice(0, 3);
  
  let message = '⭐ *Hotels met de beste beoordelingen:*\n\n';
  
  bestHotels.forEach(hotel => {
    message += `*${hotel.id}. ${hotel.name}* (${hotel.stars}⭐)\n`;
    message += `Beoordeling: ${hotel.rating}/10\n`;
    message += `Prijs: €${hotel.price_range.min} - €${hotel.price_range.max}\n`;
    message += `Voor meer info: /hotel_${hotel.id}\n\n`;
  });
  
  ctx.replyWithMarkdown(message);
};

bot.command('beste', showBestRated);
bot.hears('⭐ Beste beoordelingen', showBestRated);

// Goedkoopste hotels
const showCheapest = (ctx) => {
  const sortedHotels = [...hotelsData.hotels].sort((a, b) => a.price_range.min - b.price_range.min);
  const cheapestHotels = sortedHotels.slice(0, 3);
  
  let message = '💰 *Goedkoopste hotels:*\n\n';
  
  cheapestHotels.forEach(hotel => {
    message += `*${hotel.id}. ${hotel.name}* (${hotel.stars}⭐)\n`;
    message += `Prijs vanaf: €${hotel.price_range.min}\n`;
    message += `Beoordeling: ${hotel.rating}/10\n`;
    message += `Voor meer info: /hotel_${hotel.id}\n\n`;
  });
  
  ctx.replyWithMarkdown(message);
};

bot.command('goedkoop', showCheapest);
bot.hears('💰 Goedkoopste hotels', showCheapest);

// Starten van de bot
bot.launch().then(() => {
  console.log('Bot is actief!');
});

// Enable graceful stop
process.once('SIGINT', () => bot.stop('SIGINT'));
process.once('SIGTERM', () => bot.stop('SIGTERM')); 